/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.mach.contract.flowApvContractPayInfo.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.aspose.words.LoadFormat;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.google.common.collect.Maps;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.ProcessEngine;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.repository.ProcessDefinition;
import org.springblade.common.cache.DictBizCache;
import org.springblade.common.utils.*;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.IoUtil;
import org.springblade.core.tool.utils.ResourceUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.flow.business.service.IFlowService;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.flow.core.utils.FlowUtil;
import org.springblade.mach.contract.flowApvContractPayDetail.service.IApvContractPayDetailService;
import org.springblade.mach.contract.flowApvContractPayInfo.excel.ApvContractPayInfoExcel;
import org.springblade.mach.contract.flowApvContractPayInfo.mapper.ApvContractPayInfoMapper;
import org.springblade.mach.contract.flowApvContractPayInfo.pojo.entity.ApvContractPayInfoEntity;
import org.springblade.mach.contract.flowApvContractPayInfo.pojo.vo.ApvContractPayInfoVO;
import org.springblade.mach.contract.flowApvContractPayInfo.service.IApvContractPayInfoService;
import org.springblade.mach.contract.flowApvContractPayDetail.pojo.entity.ApvContractPayDetailEntity;
import org.springblade.mach.contract.flowApvContractPayInfo.wrapper.ApvContractPayInfoWrapper;
import org.springblade.mach.contract.flowApvContractPayPaymentReal.pojo.entity.ApvContractPayPaymentRealEntity;
import org.springblade.mach.contract.flowApvContractPayPaymentReal.service.IApvContractPayPaymentRealService;
import org.springblade.mach.contract.flowOutContractPayInfo.pojo.entity.FlowOutContractPayInfoEntity;
import org.springblade.mach.pm.projectEqDetails.pojo.entity.FlowProjectEqDetailsEntity;
import org.springblade.modules.resource.builder.OssBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;

/**
 * 付款审批 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Slf4j
@Service
@AllArgsConstructor
public class ApvContractPayInfoServiceImpl extends BaseServiceImpl<ApvContractPayInfoMapper, ApvContractPayInfoEntity> implements IApvContractPayInfoService {


	private final OssBuilder ossBuilder;

	private final IFlowService flowService;

	private final IApvContractPayDetailService detailService;

	private final IApvContractPayPaymentRealService payPaymentRealService;

	// 确保常量定义正确
	private static final BigDecimal AMOUNT_THRESHOLD = new BigDecimal("20000");

	private static final String MATERIAL_PURCHASE_TYPE = "1";
	private static final String INSTALLATION_PROJECT_TYPE = "2";

	@Override
	public IPage<ApvContractPayInfoVO> selectApvContractPayInfoPage(IPage<ApvContractPayInfoVO> page, ApvContractPayInfoVO apvContractPayInfo) {
		return page.setRecords(baseMapper.selectApvContractPayInfoPage(page, apvContractPayInfo));
	}

	@Override
	public List<ApvContractPayInfoExcel> exportApvContractPayInfo(Wrapper<ApvContractPayInfoEntity> queryWrapper) {
		List<ApvContractPayInfoExcel> apvContractPayInfoList = baseMapper.exportApvContractPayInfo(queryWrapper);
		//apvContractPayInfoList.forEach(apvContractPayInfo -> {
		//	apvContractPayInfo.setTypeName(DictCache.getValue(DictEnum.YES_NO, ApvContractPayInfo.getType()));
		//});
		return apvContractPayInfoList;
	}

	@Override
	public void viewApvPdfOutStream(Long id, HttpServletRequest request, HttpServletResponse response) {
        // poi-tl模版
        String poiTl = "classpath:/poi-tl/apv_contract_payinfo.docx";
        try {
            // 获取付款审批信息
            ApvContractPayInfoEntity entity = this.getById(id);
            if (Func.isEmpty(entity)) {
                throw new RuntimeException("付款审批信息不存在");
            }
			ApvContractPayInfoVO detail = ApvContractPayInfoWrapper.build().entityVO(entity);
			List<ApvContractPayDetailEntity> list = detailService.lambdaQuery().eq(ApvContractPayDetailEntity::getPayInfoId, id).list();
			if (Func.isNotEmpty(list)) {
				detail.setPayDetailList(list);
			}
			// 处理字典信息
            detail.setBankChange("1".equals(detail.getBankChange()) ? "是" : "否");
            detail.setInvoiceStatus("1".equals(detail.getInvoiceStatus()) ? "已开" : "否");
            detail.setInstoreStatus("1".equals(detail.getInstoreStatus()) ? "是" : "否");

            List<ApvContractPayDetailEntity> payDetailList = detail.getPayDetailList();
            for (ApvContractPayDetailEntity apvContractPayDetailEntity : payDetailList) {
                apvContractPayDetailEntity.setType(DictBizCache.getValue("out_contract_pay_type", apvContractPayDetailEntity.getType()));
            }
            detail.setPayDetailList(payDetailList);

            // 先生成合同word，再生成PDF
            Resource resource = ResourceUtil.getResource(poiTl);
            LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
            // 配置
            Configure configure = Configure.builder()
                .bind("payDetailList", policy)
                .useSpringEL()
                .build();

            BigDecimal amount = detail.getAmount();
            detail.setTotalAmountInWords(ConvertUpMoney.toChinese(amount));

            Map<String, Object> data = new HashMap<>();
            data.put("payDetailList", detail.getPayDetailList());
            data.put("payinfo", detail);
            data.put("applicationDate", DateUtil.formatDate(detail.getCreateTime()));
            data.put("factoryName", DictBizCache.getValue("factory_name", detail.getPartyA()));

            // 通过 XWPFTemplate 编译文件并渲染数据到模板中
            XWPFTemplate template = XWPFTemplate.compile(resource.getInputStream(), configure).render(data);

            // 生成到word字符流
            ByteArrayOutputStream fos = new ByteArrayOutputStream();
            template.writeAndClose(fos);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(fos.toByteArray());
            fos = null;

            // 生成PDF 输出到页面
            String fileName = StrUtil.format("【%s】付款审批.pdf", detail.getCode());
            ServletUtil.setDownloadHeader(request, response, fileName);
            ServletOutputStream outputStream = response.getOutputStream();
            DocUtil.getInstance().createPdfWithInputStream(inputStream, LoadFormat.DOCX, outputStream);
        } catch (Exception e) {
            LogUtils.error(getClass(), "生成付款审批异常:", e);
            throw new RuntimeException("查看付款审批PDF失败: " + e.getMessage());
        }
    }


	@Override
	public void viewAttachmentsPdfOutStream(Long id, HttpServletRequest request, HttpServletResponse response) {
		try {
			// 获取付款审批信息
			List<ApvContractPayPaymentRealEntity> list = payPaymentRealService.lambdaQuery()
				.eq(ApvContractPayPaymentRealEntity::getPayInfoId, id).list();
			if (Func.isEmpty(list)) {
				throw new RuntimeException("付款审批信息不存在");
 			}
			ApvContractPayInfoEntity payInfo = this.lambdaQuery()
				.eq(ApvContractPayInfoEntity::getId, id)
				.one();
			List<byte[]> inputStreamList = new ArrayList<>();
			for (ApvContractPayPaymentRealEntity real : list) {
				String contractFiles = real.getContractFiles();
				if (StrUtil.isNotBlank(contractFiles)) {
					try {
						// 处理 mach-upload 组件返回的格式：|upload/path/file.pdf?fn=文件名.pdf
						if (contractFiles.startsWith("|")) {
							// 按 | 分割并过滤空字符串
							String[] fileEntries = contractFiles.split("\\|");
							for (String fileEntry : fileEntries) {
								if (StrUtil.isNotBlank(fileEntry)) {
									// 提取文件路径（去掉 ?fn= 部分）
									String filePath = fileEntry;
									if (fileEntry.contains("?fn=")) {
										filePath = fileEntry.substring(0, fileEntry.indexOf("?fn="));
									}

									try {
										String f = ossBuilder.template().fileLink(filePath);
										InputStream inputStream = URLUtil.getStream(URLUtil.toUrlForHttp(f));
										byte[] pdfBytes = IoUtil.readToByteArray(inputStream);
										inputStreamList.add(pdfBytes);
										log.info("成功加载合同文件: " + filePath);
									} catch (Exception e) {
										log.warn("获取合同文件失败: " + filePath, e);
									}
								}
							}
						}
						// 兼容JSON格式的文件数据
						else if (contractFiles.startsWith("[") && contractFiles.endsWith("]")) {
							// 移除方括号并按逗号分割
							contractFiles = contractFiles.substring(1, contractFiles.length() - 1);
							String[] fileEntries = contractFiles.split("\\},\\{");
							for (String fileEntry : fileEntries) {
								// 清理JSON格式，提取link字段
								fileEntry = fileEntry.replaceAll("[\\[\\]{}]", "");
								String[] pairs = fileEntry.split(",");
								for (String pair : pairs) {
									if (pair.contains("\"link\":")) {
										String link = pair.split(":")[1].replaceAll("\"", "").trim();
										if (StrUtil.isNotBlank(link)) {
											try {
												String f = ossBuilder.template().fileLink(link);
												InputStream inputStream = URLUtil.getStream(URLUtil.toUrlForHttp(f));
												byte[] pdfBytes = IoUtil.readToByteArray(inputStream);
												inputStreamList.add(pdfBytes);
											} catch (Exception e) {
												log.warn("获取合同文件失败: " + link, e);
											}
										}
									}
								}
							}
						}
						// 兼容逗号分隔的文件路径
						else {
							String[] contractFileArr = contractFiles.split(",");
							for (String contractFile : contractFileArr) {
								if(StrUtil.isNotBlank(contractFile) && contractFile.contains("|")){
									contractFile = contractFile.substring(contractFile.indexOf("|")+1);
								}
								try {
									String f = ossBuilder.template().fileLink(contractFile);
									InputStream inputStream = URLUtil.getStream(URLUtil.toUrlForHttp(f));
									byte[] pdfBytes = IoUtil.readToByteArray(inputStream);
									inputStreamList.add(pdfBytes);
								} catch (Exception e) {
									log.warn("获取合同文件失败: " + contractFile, e);
								}
							}
						}
					} catch (Exception e) {
						log.warn("解析合同文件数据失败: " + contractFiles, e);
					}
				}
			}

			// 检查是否有有效的合同文件
			if (inputStreamList.isEmpty()) {
				// 返回空的PDF或错误信息
				response.setContentType("application/json;charset=UTF-8");
				response.getWriter().write("{\"error\":\"未找到相关合同文件\"}");
				return;
			}

			String contractName = "付款审批【"+payInfo.getCode()+"】关联合同";
			String fileName = contractName+".pdf";
			ServletUtil.setDownloadHeader(request,response,fileName);
			ServletOutputStream outputStream = response.getOutputStream();

			// 如果只有一个文件，直接输出
			if (inputStreamList.size() == 1) {
				outputStream.write(inputStreamList.get(0));
				outputStream.flush();
			} else {
				// 多个文件合并
				InputStream[] isAry = new InputStream[inputStreamList.size()];
				for (int i = 0; i < inputStreamList.size(); i++) {
					byte[] pdfBytes = inputStreamList.get(i);
					isAry[i] = new ByteArrayInputStream(pdfBytes);
				}
				PdfUtil.getInstance().concatPdf(isAry,outputStream);
			}

//			// 获取附件路径 |upload/20250514/b8bee587805b5b2d2f0751ab25f456f5.pdf?fn=大包设计电气柜合同签订.pdf
//			String attachmentPath = detail.getAttachments();
//			// 去掉|fn=大包设计电气柜合同签订.pdf
//			if(StrUtil.isNotBlank(attachmentPath) && attachmentPath.contains("|")){
//				attachmentPath = attachmentPath.substring(attachmentPath.indexOf("|")+1);
//			}
//			if(StrUtil.isNotBlank(attachmentPath)){
//				// 解码contractName  b8bee587805b5b2d2f0751ab25f456f5.pdf?fn=大包设计电气柜合同签订.pdf
//				String contractName = attachmentPath.substring(attachmentPath.indexOf("fn=")+3);
//				String f = ossBuilder.template().fileLink(attachmentPath);
//				InputStream inputStream = URLUtil.getStream(URLUtil.toUrlForHttp(f));
//				byte[] pdfBytes =  IoUtil.readToByteArray(inputStream);
//				ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(pdfBytes);
//				ServletUtil.setDownloadHeader(request,response,contractName);
//				ServletUtil.write(response, byteArrayInputStream, "application/octet-stream");
//			}
		} catch (Exception e) {
			throw new RuntimeException("查看付款审批附件失败: " + e.getMessage());
		}
	}

	@Override
	public void submitByDef(ApvContractPayInfoVO apvContractPayInfo) {
		// 保存付款审批信息
		boolean success = this.saveOrUpdate(apvContractPayInfo);

		// 保存支付方式数据
		if (success && apvContractPayInfo.getPayDetailList() != null && !apvContractPayInfo.getPayDetailList().isEmpty()) {
			// 先删除原有的支付方式
			detailService.lambdaUpdate()
				.eq(ApvContractPayDetailEntity::getPayInfoId, apvContractPayInfo.getId())
				.remove();

			// 保存新的支付方式
			for (ApvContractPayDetailEntity detail : apvContractPayInfo.getPayDetailList()) {
				detail.setPayInfoId(apvContractPayInfo.getId());
				detailService.save(detail);
			}
		}

		// 保存请款单数据
		if (success && apvContractPayInfo.getPaymentRealList() != null && !apvContractPayInfo.getPaymentRealList().isEmpty()) {
			// 先删除原有的请款单
			payPaymentRealService.lambdaUpdate()
				.eq(ApvContractPayPaymentRealEntity::getPayInfoId, apvContractPayInfo.getId())
				.remove();

			// 保存新的请款单
			for (ApvContractPayPaymentRealEntity paymentReal : apvContractPayInfo.getPaymentRealList()) {
				paymentReal.setPayInfoId(apvContractPayInfo.getId());
				paymentReal.setCreateTime(new Date());
				// 处理附件多文件上传
				if (StrUtil.isNotBlank(paymentReal.getAttachmentFiles())) {
					paymentReal.setAttachmentFiles(paymentReal.getAttachmentFiles().replaceAll("\\[|\\]", ""));
				}
				payPaymentRealService.save(paymentReal);
			}
		}
		//根据type类型判断是物资采购合同付款还是安装工程合同付款
		String type = apvContractPayInfo.getType();
		if (MATERIAL_PURCHASE_TYPE.equals(type)) {
			this.mpAdudit(apvContractPayInfo);
		} else if (INSTALLATION_PROJECT_TYPE.equals(type)) {
			this.iwpAdudit(apvContractPayInfo);
		} else {
			log.warn("没有该合同类型");
		}


	}


	/**
	 * 重新编辑付款信息
	 *
	 * @param apvContractPayInfo 付款审批信息
	 */
	private void reEditPayInfo(ApvContractPayInfoVO apvContractPayInfo) {
		// 保存付款审批信息
		this.saveOrUpdate(apvContractPayInfo);

		// 更新支付方式
		updatePayDetail(apvContractPayInfo);
	}

	/**
	 * 更新支付明细
	 *
	 * @param apvContractPayInfo 付款审批信息
	 */
	private void updatePayDetail(ApvContractPayInfoVO apvContractPayInfo) {
		if (apvContractPayInfo.getPayDetailList() != null && !apvContractPayInfo.getPayDetailList().isEmpty()) {
			// 先删除原有的支付方式
			detailService.lambdaUpdate()
				.eq(ApvContractPayDetailEntity::getPayInfoId, apvContractPayInfo.getId())
				.remove();

			// 保存新的支付方式
			for (ApvContractPayDetailEntity detail : apvContractPayInfo.getPayDetailList()) {
				detail.setPayInfoId(apvContractPayInfo.getId());
				detailService.save(detail);
			}
		}
	}

	/**
	 * 更新合同付款完成状态
	 *
	 * @param apvContractPayInfo 付款审批信息
	 */
	private void updateContractPayEnd(ApvContractPayInfoVO apvContractPayInfo) {
		// 根据业务需求更新相关合同的付款状态
		// 此处根据实际业务逻辑实现
		LogUtils.info(getClass(), "付款审批[" + apvContractPayInfo.getCode() + "]完成，更新合同付款状态");
	}


	// ========================================物资采购合同付款============================================================

	public R<?> mpAdudit(ApvContractPayInfoVO apvContractPayInfo) {
		Map<String, Object> vars = Maps.newHashMap();
		if (StringUtils.isBlank(apvContractPayInfo.getProcInsId())) {
			// 启动流程
			return startMpProcess(apvContractPayInfo, vars);
		} else {
			String flag = apvContractPayInfo.getFlow().getFlag();
			String taskDefKey = apvContractPayInfo.getFlow().getTaskDefinitionKey();
			String processDefinitionIdStr = apvContractPayInfo.getFlow().getProcessDefinitionId();

			switch (taskDefKey) {
				// 合同责任人
				case "contract_resp":
					this.reEditPayInfo(apvContractPayInfo);
					break;
				case "supply_chain_center":
					backNode(flag, vars, ApvContractPayInfoEntity.taskKeys.getInt(apvContractPayInfo.getBackTaskKey()), apvContractPayInfo);
					break;
				// 分管高管
				case "senior":
					backNode(flag, vars, FlowOutContractPayInfoEntity.taskKeys.getInt(apvContractPayInfo.getBackTaskKey()), apvContractPayInfo);
					if (vars.get("pass").equals(1)) {
						seniorUser(apvContractPayInfo, vars);
					}
					break;
				case "fpd"://计财部
					// 财务审批节点需要更新支付方式
					this.updatePayDetail(apvContractPayInfo);
					backNode(flag, vars, FlowOutContractPayInfoEntity.taskKeys.getInt(apvContractPayInfo.getBackTaskKey()), apvContractPayInfo);
					break;
				case "gm":
					backNode(flag, vars, FlowOutContractPayInfoEntity.taskKeys.getInt(apvContractPayInfo.getBackTaskKey()), apvContractPayInfo);
					break;
				default:
					break;
			}

			// 完成任务
			flowService.completeTask(apvContractPayInfo.getFlow().getTaskId(), apvContractPayInfo.getFlow().getProcessInstanceId(), apvContractPayInfo.getFlow().getComment(), vars);

			// 记录审批人
			String approverNameStr = this.getById(apvContractPayInfo.getId()).getApproverName();
			List<String> approverNameList = Func.toStrList(approverNameStr);
			LinkedHashSet<String> approverNameSet = new LinkedHashSet<>(approverNameList);
			approverNameStr = Func.join(approverNameSet);
			this.lambdaUpdate()
				.set(ApvContractPayInfoEntity::getApproverName, approverNameStr)
				.eq(ApvContractPayInfoEntity::getId, apvContractPayInfo.getId())
				.update();

			return R.success("提交成功");
		}
	}

	private static void backNode(String flag, Map<String, Object> vars, Integer taskKeys, ApvContractPayInfoVO apvContractPayInfo) {
		if ("1".equals(flag)) {
			vars.put("pass", 1);
		} else {
			vars.put("pass", 0);
			vars.put("back", taskKeys);//驳回到某个节点
			apvContractPayInfo.getFlow().setComment("[驳回]" + apvContractPayInfo.getFlow().getComment());
		}
	}

	private static void seniorUser(ApvContractPayInfoVO apvContractPayInfo, Map<String, Object> vars) {
		if (apvContractPayInfo == null || vars == null) {
			LogUtils.warn(ApvContractPayInfoServiceImpl.class, "apvContractPayInfo 或 vars 为 null，无法继续执行 seniorUser 方法");
			return;
		}

		String partyA = apvContractPayInfo.getPartyA(); // 生产基地转为公司代号
		LogUtils.debug(ApvContractPayInfoServiceImpl.class, "当前生产基地: " + partyA);
		String seniorUser = "";
		String fpdUser = "";
		String fpdPayUser = "";
		String evUser = "";
		if (FlowProjectEqDetailsEntity.FACTORY_TYPE[5].equals(partyA)){ // 北京
			seniorUser = MachUtils.getManagerByRole("北京工厂分管高管");
			fpdUser = MachUtils.getManagerByRole("北京工厂财务部");
			fpdPayUser = MachUtils.getManagerByRole("北京工厂财务部审批付款");
			vars.put("addr",0);
			updateUser(apvContractPayInfo, vars,  seniorUser, fpdUser, fpdPayUser, evUser);
		}else if (FlowProjectEqDetailsEntity.FACTORY_TYPE[0].equals(partyA)) { // 天津
			seniorUser = MachUtils.getManagerByRole("天津工厂分管高管");
			fpdUser = MachUtils.getManagerByRole("天津工厂财务部");
			fpdPayUser = MachUtils.getManagerByRole("天津工厂财务部审批付款");
			evUser = MachUtils.getManagerByRole("天津工厂常务副总");
			vars.put("addr",1);
			updateUser(apvContractPayInfo, vars, seniorUser, fpdUser, fpdPayUser, evUser);
		}else{
			seniorUser = MachUtils.getManagerByRole("江苏工厂分管高管");
			fpdUser = MachUtils.getManagerByRole("江苏工厂财务部");
			fpdPayUser = MachUtils.getManagerByRole("江苏工厂财务部审批付款");
			evUser = MachUtils.getManagerByRole("江苏工厂常务副总");
			vars.put("addr",2);
			updateUser(apvContractPayInfo, vars, seniorUser, fpdUser, fpdPayUser,  evUser);
		}

	}

	private static void updateUser(ApvContractPayInfoVO apvContractPayInfo, Map<String, Object> vars,
								   String seniorUser, String fpdUser, String fpdPayUser, String evUser) {
		if (apvContractPayInfo.getAmount().compareTo(AMOUNT_THRESHOLD) >= 0) {
			vars.put("pay", 3);
			vars.put("fpdUser", fpdUser);
		} else {
			vars.put("pay", 2);
		}
		vars.put("evUser", evUser);
		vars.put("fpdPayUser", fpdPayUser);
		vars.put("seniorUser", seniorUser);
	}

	// ========================================安装工程合同付款============================================================

	public R<?> iwpAdudit(ApvContractPayInfoVO apvContractPayInfo) {
		Map<String, Object> vars = Maps.newHashMap();
		if (StringUtils.isBlank(apvContractPayInfo.getProcInsId())) {
			// 启动流程
			return startIwpProcess(apvContractPayInfo, vars);
		} else {
			String flag = apvContractPayInfo.getFlow().getFlag();
			String taskDefKey = apvContractPayInfo.getFlow().getTaskDefinitionKey();
			String processDefinitionIdStr = apvContractPayInfo.getFlow().getProcessDefinitionId();

			switch (taskDefKey) {
				// 合同责任人
				case "install_contract_holder":
					this.reEditPayInfo(apvContractPayInfo);
					break;
				// 实施服务中心主任
				case "isc_director":
					backNode(flag, vars, FlowOutContractPayInfoEntity.taskKeys.getInt(apvContractPayInfo.getBackTaskKey()), apvContractPayInfo);
					if (vars.get("pass").equals(1)) {
						updateTraceNode(apvContractPayInfo, vars);
					}
					break;
				//常务副总
				case "ev":
					backNode(flag, vars, FlowOutContractPayInfoEntity.taskKeys.getInt(apvContractPayInfo.getBackTaskKey()), apvContractPayInfo);
					vars.put("finishAudit",1);
					break;
				//计财部
				case "fpd":
					// 财务审批节点需要更新支付方式
					this.updatePayDetail(apvContractPayInfo);
					backNode(flag, vars, FlowOutContractPayInfoEntity.taskKeys.getInt(apvContractPayInfo.getBackTaskKey()), apvContractPayInfo);
					break;
				//总经理
				case "gm":
					backNode(flag, vars, FlowOutContractPayInfoEntity.taskKeys.getInt(apvContractPayInfo.getBackTaskKey()), apvContractPayInfo);
					vars.put("finishAudit",1);
					break;
				default:
					break;
			}

			// 完成任务
			flowService.completeTask(apvContractPayInfo.getFlow().getTaskId(), apvContractPayInfo.getFlow().getProcessInstanceId(), apvContractPayInfo.getFlow().getComment(), vars);

			// 记录审批人
			String approverNameStr = this.getById(apvContractPayInfo.getId()).getApproverName();
			List<String> approverNameList = Func.toStrList(approverNameStr);
			LinkedHashSet<String> approverNameSet = new LinkedHashSet<>(approverNameList);
			approverNameStr = Func.join(approverNameSet);
			this.lambdaUpdate()
				.set(ApvContractPayInfoEntity::getApproverName, approverNameStr)
				.eq(ApvContractPayInfoEntity::getId, apvContractPayInfo.getId())
				.update();

			return R.success("提交成功");
		}
	}

	private void updateTraceNode(ApvContractPayInfoVO apvContractPayInfo, Map<String, Object> vars) {
		if  (apvContractPayInfo.getAmount().compareTo(AMOUNT_THRESHOLD) >= 0) {
			vars.put("pay", 3);//大于2w
			//大于2w各个省份的各个审批节点角色用户的设置
		}else{
			vars.put("pay", 2);
			//小于2w各个省份的各个审批节点角色用户的设置
		}
		seniorUser(apvContractPayInfo, vars);


	}


	private R<?> startMpProcess(ApvContractPayInfoVO apvContractPayInfo, Map<String, Object> vars) {
		vars.put("respUser", AuthUtil.getUserAccount());//记录合同责任发起人
		vars.put("title",apvContractPayInfo.getCode());//编号
		vars.put("supplyUser",MachUtils.getManagerByRole("供应链中心经理"));
		flowService.startProcessInstanceByKey(
			FlowUtil.PD_MP_PAYMENT_AUDIT[0],
			FlowUtil.PD_MP_PAYMENT_AUDIT[1],
			String.valueOf(apvContractPayInfo.getId()), vars);
		//记录申请人
		this.lambdaUpdate()
			.set(ApvContractPayInfoEntity::getApplicantName, AuthUtil.getNickName())
			.eq(ApvContractPayInfoEntity::getId, apvContractPayInfo.getId())
			.update();
		LogUtils.info(getClass(), org.springblade.core.tool.utils.DateUtil.format(new Date(), "yy--MM-dd HH:mm:ss") + "启动物资采购合同付款申请[" + apvContractPayInfo.getCode() + "]的流程");

		return R.success("流程启动成功");
	}


	private R<?> startIwpProcess(ApvContractPayInfoVO apvContractPayInfo, Map<String, Object> vars) {
		vars.put("installUser", AuthUtil.getUserAccount());//安装合同责任人
		vars.put("title",apvContractPayInfo.getCode());//编号
		vars.put("iscUser","");//安装服务中心主任
		flowService.startProcessInstanceByKey(
			FlowUtil.PD_MP_PAYMENT_AUDIT[0],
			FlowUtil.PD_MP_PAYMENT_AUDIT[1],
			String.valueOf(apvContractPayInfo.getId()), vars);
		//记录申请人
		this.lambdaUpdate()
			.set(ApvContractPayInfoEntity::getApplicantName, AuthUtil.getNickName())
			.eq(ApvContractPayInfoEntity::getId, apvContractPayInfo.getId())
			.update();
		LogUtils.info(getClass(), org.springblade.core.tool.utils.DateUtil.format(new Date(), "yy--MM-dd HH:mm:ss") + "启动安装工程合同付款申请[" + apvContractPayInfo.getCode() + "]的流程");

		return R.success("流程启动成功");
	}


	/**
	 * 获取流程处理表单
	 * {@link org.springblade.flow.business.service.impl.FlowServiceImpl#getAuditFormVue 动态调用本方法}
	 *
	 * @param flow
	 * @return
	 */
	@Override
	public String auditForm(BladeFlow flow) {
		String view = "flowContract/flowApvContractPayInfo/flowApvContractPayInfoAudit";

		if (StringUtil.isBlank(flow.getBusinessId())) {
			return view;
		}

		if (flow.isFinishTask()) {
			return view;
		}

		// 环节编号
		String taskDefKey = flow.getTaskDefinitionKey();
		if (StringUtil.isBlank(taskDefKey)) {
			return view;
		}

		String processDefinitionId = flow.getProcessDefinitionId();
		ProcessEngine processEngine = flowService.getProcessEngine();
		// 获取流程定义Key
		RepositoryService repositoryService = processEngine.getRepositoryService();
		ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
			.processDefinitionId(processDefinitionId)
			.singleResult();
		String processDefinitionKey = processDefinition.getKey(); // 获取流程定义Key

		// 根据任务节点返回对应的审批页面
		switch (taskDefKey) {
			case "apply_edit":
				view = "flowContract/flowApvContractPayInfo/flowApvContractPayInfoReEdit";
				break;
			case "fpd": // 财务审批节点，需要编辑支付方式
				view = "flowContract/flowApvContractPayInfo/flowApvContractPayInfoFinanceAudit";
				break;
			default:
				view = "flowContract/flowApvContractPayInfo/flowApvContractPayInfoAudit";
				break;
		}
		return view;
	}

	/**
	 * 获取流程表单详情数据
	 *
	 * @param flow 流程信息
	 * @return 表单详情数据
	 */
	@Override
	public R auditFormDetail(BladeFlow flow) {
		ApvContractPayInfoEntity entity = this.getById(Long.valueOf(flow.getBusinessId()));
		ApvContractPayInfoVO apvContractPayInfo = ApvContractPayInfoWrapper.build().entityVO(entity);

		// 获取支付方式列表
		List<ApvContractPayDetailEntity> payDetailList = detailService.lambdaQuery()
			.eq(ApvContractPayDetailEntity::getPayInfoId, entity.getId())
			.list();
		apvContractPayInfo.setPayDetailList(payDetailList);

		// 获取请款单列表
		List<ApvContractPayPaymentRealEntity> paymentRealList = payPaymentRealService.lambdaQuery()
			.eq(ApvContractPayPaymentRealEntity::getPayInfoId, entity.getId())
			.list();
		apvContractPayInfo.setPaymentRealList(paymentRealList);

		// 设置流程信息
		apvContractPayInfo.setFlow(flow);

		return R.data(apvContractPayInfo);
	}

	/**
	 * 流程审批
	 *
	 * @param apvContractPayInfo 付款审批信息
	 * @return 审批结果
	 */
	@Override
	@Transactional(readOnly = false)
	public R audit(ApvContractPayInfoVO apvContractPayInfo) {
		// 根据流程定义Key判断是哪种类型的付款审批
		String processDefinitionKey = apvContractPayInfo.getFlow().getProcessDefinitionKey();

		if (StrUtil.startWith(processDefinitionKey, "mp_payment")) {
			// 物资采购合同付款审批
			return mpAdudit(apvContractPayInfo);
		} else {
			// 安装工程合同付款审批
			return iwpAdudit(apvContractPayInfo);
		}
	}

}
