<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:flowable="http://flowable.org/bpmn" xmlns:activiti="http://activiti.org/bpmn" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" targetNamespace="http://bpmn.io/schema/bpmn">
  <process id="install_works_payment" name="安装工程合同付款" isExecutable="true">
    <startEvent id="startEvent_1" name="开始" flowable:formKey="/mach/apvContractPayInfo/auditForm">
      <outgoing>Flow_1qk6nit</outgoing>
    </startEvent>
    <userTask id="install_contract_holder" name="安装合同责任人" flowable:assignee="${installUser}">
      <incoming>Flow_0wkjyh3</incoming>
      <outgoing>Flow_19moyuq</outgoing>
    </userTask>
    <userTask id="isc_director" name="实施服务中心主任" flowable:assignee="${iscUser}">
      <incoming>Flow_19moyuq</incoming>
      <incoming>Flow_0q3r9ax</incoming>
      <incoming>Flow_1qk6nit</incoming>
      <outgoing>Flow_115t73g</outgoing>
    </userTask>
    <sequenceFlow id="Flow_19moyuq" name="填写付款申请单" sourceRef="install_contract_holder" targetRef="isc_director" />
    <endEvent id="Event_1ydzk1d">
      <incoming>Flow_1h0qcdy</incoming>
      <incoming>Flow_1h44ki1</incoming>
    </endEvent>
    <sequenceFlow id="Flow_1qk6nit" sourceRef="startEvent_1" targetRef="isc_director" />
    <exclusiveGateway id="Gateway_0f62pb2">
      <incoming>Flow_115t73g</incoming>
      <outgoing>Flow_0l1v54p</outgoing>
      <outgoing>Flow_1el3gp6</outgoing>
      <outgoing>Flow_0drkn8n</outgoing>
    </exclusiveGateway>
    <sequenceFlow id="Flow_115t73g" name="通过" sourceRef="isc_director" targetRef="Gateway_0f62pb2" />
    <exclusiveGateway id="Gateway_138boi8">
      <incoming>Flow_0l1v54p</incoming>
      <outgoing>Flow_0tfkdbm</outgoing>
      <outgoing>Flow_09dlqwu</outgoing>
    </exclusiveGateway>
    <sequenceFlow id="Flow_0l1v54p" name="小于等于2w" sourceRef="Gateway_0f62pb2" targetRef="Gateway_138boi8">
      <conditionExpression xsi:type="tFormalExpression">${pay&lt;=2 &amp;&amp; pass==1}</conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="Gateway_13gg7b8">
      <incoming>Flow_1el3gp6</incoming>
      <outgoing>Flow_0n86kck</outgoing>
      <outgoing>Flow_0qwiwe3</outgoing>
    </exclusiveGateway>
    <sequenceFlow id="Flow_1el3gp6" name="大于2w" sourceRef="Gateway_0f62pb2" targetRef="Gateway_13gg7b8">
      <conditionExpression xsi:type="tFormalExpression">${pay&gt;2 &amp;&amp; pass==1}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="Flow_0tfkdbm" name="北京" sourceRef="Gateway_138boi8" targetRef="fpd_pay">
      <conditionExpression xsi:type="tFormalExpression">${addr==0}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="Flow_09dlqwu" name="天津/江苏" sourceRef="Gateway_138boi8" targetRef="ev">
      <conditionExpression xsi:type="tFormalExpression">${addr&gt;0}</conditionExpression>
    </sequenceFlow>
    <userTask id="gm" name="总经理" flowable:assignee="sunchaobo">
      <incoming>Flow_0n86kck</incoming>
      <incoming>Flow_1v3avca</incoming>
      <outgoing>Flow_0sgjcbn</outgoing>
    </userTask>
    <sequenceFlow id="Flow_0n86kck" name="北京" sourceRef="Gateway_13gg7b8" targetRef="gm">
      <conditionExpression xsi:type="tFormalExpression">${addr==0}</conditionExpression>
    </sequenceFlow>
    <userTask id="fpd" name="计财部" flowable:assignee="${fpdUser}">
      <incoming>Flow_1w0dzdr</incoming>
      <incoming>Flow_1umrhkj</incoming>
      <outgoing>Flow_0c6e52d</outgoing>
    </userTask>
    <sequenceFlow id="Flow_0sgjcbn" sourceRef="gm" targetRef="Gateway_1vfuhn0" />
    <sequenceFlow id="Flow_0c6e52d" sourceRef="fpd" targetRef="Gateway_16ueh5k" />
    <exclusiveGateway id="Gateway_1vfuhn0">
      <incoming>Flow_0sgjcbn</incoming>
      <incoming>Flow_0v00vp8</incoming>
      <outgoing>Flow_1w0dzdr</outgoing>
      <outgoing>Flow_0os2ri1</outgoing>
      <outgoing>Flow_0fvmq9w</outgoing>
    </exclusiveGateway>
    <sequenceFlow id="Flow_1w0dzdr" sourceRef="Gateway_1vfuhn0" targetRef="fpd">
      <conditionExpression xsi:type="tFormalExpression">${pay&gt;2 &amp;&amp; addr&gt;0}</conditionExpression>
    </sequenceFlow>
    <userTask id="ev" name="常务副总" flowable:assignee="${evpUser}">
      <incoming>Flow_0qwiwe3</incoming>
      <incoming>Flow_09dlqwu</incoming>
      <incoming>Flow_0scoqms</incoming>
      <outgoing>Flow_0v00vp8</outgoing>
    </userTask>
    <sequenceFlow id="Flow_0qwiwe3" name="天津/江苏" sourceRef="Gateway_13gg7b8" targetRef="ev">
      <conditionExpression xsi:type="tFormalExpression">${addr&gt;0 }</conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="Gateway_16ueh5k">
      <incoming>Flow_0c6e52d</incoming>
      <outgoing>Flow_1h0qcdy</outgoing>
      <outgoing>Flow_1v3avca</outgoing>
      <outgoing>Flow_13vgcjl</outgoing>
    </exclusiveGateway>
    <sequenceFlow id="Flow_1h0qcdy" sourceRef="Gateway_16ueh5k" targetRef="Event_1ydzk1d" />
    <sequenceFlow id="Flow_1v3avca" sourceRef="Gateway_16ueh5k" targetRef="gm" />
    <userTask id="fpd_pay" name="计财部(付款)" flowable:assignee="${fpdPayUser}">
      <incoming>Flow_0os2ri1</incoming>
      <incoming>Flow_0tfkdbm</incoming>
      <outgoing>Flow_1h44ki1</outgoing>
    </userTask>
    <sequenceFlow id="Flow_1h44ki1" sourceRef="fpd_pay" targetRef="Event_1ydzk1d" />
    <sequenceFlow id="Flow_0v00vp8" sourceRef="ev" targetRef="Gateway_1vfuhn0" />
    <sequenceFlow id="Flow_0os2ri1" sourceRef="Gateway_1vfuhn0" targetRef="fpd_pay">
      <conditionExpression xsi:type="tFormalExpression">${pass==1 &amp;&amp; finishAudit==1}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="Flow_0fvmq9w" sourceRef="Gateway_1vfuhn0" targetRef="Gateway_0gpwecj" />
    <exclusiveGateway id="Gateway_0gpwecj">
      <incoming>Flow_0fvmq9w</incoming>
      <incoming>Flow_0drkn8n</incoming>
      <incoming>Flow_13vgcjl</incoming>
      <outgoing>Flow_0q3r9ax</outgoing>
      <outgoing>Flow_0wkjyh3</outgoing>
      <outgoing>Flow_0scoqms</outgoing>
      <outgoing>Flow_1umrhkj</outgoing>
    </exclusiveGateway>
    <sequenceFlow id="Flow_0q3r9ax" name="驳回实施服务中心" sourceRef="Gateway_0gpwecj" targetRef="isc_director">
      <conditionExpression xsi:type="tFormalExpression">${back==2}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="Flow_0drkn8n" sourceRef="Gateway_0f62pb2" targetRef="Gateway_0gpwecj" />
    <sequenceFlow id="Flow_0wkjyh3" name="驳回发起人" sourceRef="Gateway_0gpwecj" targetRef="install_contract_holder">
      <conditionExpression xsi:type="tFormalExpression">${back==1}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="Flow_13vgcjl" sourceRef="Gateway_16ueh5k" targetRef="Gateway_0gpwecj" />
    <sequenceFlow id="Flow_0scoqms" name="驳回常务副总" sourceRef="Gateway_0gpwecj" targetRef="ev">
      <conditionExpression xsi:type="tFormalExpression">${back==31}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="Flow_1umrhkj" name="驳回计财" sourceRef="Gateway_0gpwecj" targetRef="fpd">
      <conditionExpression xsi:type="tFormalExpression">${back==4}</conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_flow">
    <bpmndi:BPMNPlane id="BPMNPlane_flow" bpmnElement="install_works_payment">
      <bpmndi:BPMNShape id="BPMNShape_startEvent_1" bpmnElement="startEvent_1">
        <dc:Bounds x="232" y="312" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="239" y="355" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0fyk59k_di" bpmnElement="install_contract_holder">
        <dc:Bounds x="330" y="130" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_09fe5o8_di" bpmnElement="isc_director">
        <dc:Bounds x="550" y="290" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ydzk1d_di" bpmnElement="Event_1ydzk1d">
        <dc:Bounds x="1852" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0f62pb2_di" bpmnElement="Gateway_0f62pb2" isMarkerVisible="true">
        <dc:Bounds x="775" y="305" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_138boi8_di" bpmnElement="Gateway_138boi8" isMarkerVisible="true">
        <dc:Bounds x="775" y="145" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_13gg7b8_di" bpmnElement="Gateway_13gg7b8" isMarkerVisible="true">
        <dc:Bounds x="775" y="465" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_00asqfn_di" bpmnElement="gm">
        <dc:Bounds x="1290" y="570" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1eqlfta_di" bpmnElement="fpd">
        <dc:Bounds x="1450" y="450" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1vfuhn0_di" bpmnElement="Gateway_1vfuhn0" isMarkerVisible="true">
        <dc:Bounds x="1125" y="385" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_04weqsn_di" bpmnElement="ev">
        <dc:Bounds x="930" y="250" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_16ueh5k_di" bpmnElement="Gateway_16ueh5k" isMarkerVisible="true">
        <dc:Bounds x="1715" y="465" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1fb2um2_di" bpmnElement="fpd_pay">
        <dc:Bounds x="1450" y="280" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0gpwecj_di" bpmnElement="Gateway_0gpwecj" isMarkerVisible="true">
        <dc:Bounds x="685" y="-35" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_19moyuq_di" bpmnElement="Flow_19moyuq">
        <di:waypoint x="380" y="210" />
        <di:waypoint x="380" y="330" />
        <di:waypoint x="550" y="330" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="391" y="264" width="77" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qk6nit_di" bpmnElement="Flow_1qk6nit">
        <di:waypoint x="268" y="330" />
        <di:waypoint x="550" y="330" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_115t73g_di" bpmnElement="Flow_115t73g">
        <di:waypoint x="650" y="330" />
        <di:waypoint x="775" y="330" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="679" y="343" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0l1v54p_di" bpmnElement="Flow_0l1v54p">
        <di:waypoint x="800" y="305" />
        <di:waypoint x="800" y="195" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="780" y="247" width="60" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1el3gp6_di" bpmnElement="Flow_1el3gp6">
        <di:waypoint x="800" y="355" />
        <di:waypoint x="800" y="465" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="781" y="403" width="38" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0tfkdbm_di" bpmnElement="Flow_0tfkdbm">
        <di:waypoint x="800" y="145" />
        <di:waypoint x="800" y="70" />
        <di:waypoint x="1500" y="70" />
        <di:waypoint x="1500" y="280" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="924" y="52" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09dlqwu_di" bpmnElement="Flow_09dlqwu">
        <di:waypoint x="825" y="170" />
        <di:waypoint x="980" y="170" />
        <di:waypoint x="980" y="250" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="880" y="152" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0n86kck_di" bpmnElement="Flow_0n86kck">
        <di:waypoint x="800" y="515" />
        <di:waypoint x="800" y="720" />
        <di:waypoint x="1340" y="720" />
        <di:waypoint x="1340" y="650" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1062" y="698" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0sgjcbn_di" bpmnElement="Flow_0sgjcbn">
        <di:waypoint x="1290" y="610" />
        <di:waypoint x="1150" y="610" />
        <di:waypoint x="1150" y="435" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0c6e52d_di" bpmnElement="Flow_0c6e52d">
        <di:waypoint x="1550" y="490" />
        <di:waypoint x="1715" y="490" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1w0dzdr_di" bpmnElement="Flow_1w0dzdr">
        <di:waypoint x="1165" y="420" />
        <di:waypoint x="1220" y="490" />
        <di:waypoint x="1450" y="490" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qwiwe3_di" bpmnElement="Flow_0qwiwe3">
        <di:waypoint x="825" y="490" />
        <di:waypoint x="980" y="490" />
        <di:waypoint x="980" y="330" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="876" y="463" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1h0qcdy_di" bpmnElement="Flow_1h0qcdy">
        <di:waypoint x="1765" y="490" />
        <di:waypoint x="1870" y="490" />
        <di:waypoint x="1870" y="338" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1v3avca_di" bpmnElement="Flow_1v3avca">
        <di:waypoint x="1740" y="515" />
        <di:waypoint x="1740" y="720" />
        <di:waypoint x="1340" y="720" />
        <di:waypoint x="1340" y="650" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1h44ki1_di" bpmnElement="Flow_1h44ki1">
        <di:waypoint x="1550" y="320" />
        <di:waypoint x="1852" y="320" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0v00vp8_di" bpmnElement="Flow_0v00vp8">
        <di:waypoint x="1030" y="290" />
        <di:waypoint x="1150" y="290" />
        <di:waypoint x="1150" y="385" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0os2ri1_di" bpmnElement="Flow_0os2ri1">
        <di:waypoint x="1157" y="392" />
        <di:waypoint x="1220" y="320" />
        <di:waypoint x="1450" y="320" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fvmq9w_di" bpmnElement="Flow_0fvmq9w">
        <di:waypoint x="1150" y="385" />
        <di:waypoint x="1150" y="-10" />
        <di:waypoint x="735" y="-10" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0q3r9ax_di" bpmnElement="Flow_0q3r9ax">
        <di:waypoint x="685" y="-10" />
        <di:waypoint x="600" y="-10" />
        <di:waypoint x="600" y="290" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="599" y="-28" width="88" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0drkn8n_di" bpmnElement="Flow_0drkn8n">
        <di:waypoint x="775" y="330" />
        <di:waypoint x="710" y="330" />
        <di:waypoint x="710" y="15" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wkjyh3_di" bpmnElement="Flow_0wkjyh3">
        <di:waypoint x="685" y="-10" />
        <di:waypoint x="380" y="-10" />
        <di:waypoint x="380" y="130" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="507" y="-28" width="55" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13vgcjl_di" bpmnElement="Flow_13vgcjl">
        <di:waypoint x="1740" y="465" />
        <di:waypoint x="1740" y="-10" />
        <di:waypoint x="735" y="-10" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0scoqms_di" bpmnElement="Flow_0scoqms">
        <di:waypoint x="710" y="15" />
        <di:waypoint x="710" y="290" />
        <di:waypoint x="930" y="290" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="692" y="150" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1umrhkj_di" bpmnElement="Flow_1umrhkj">
        <di:waypoint x="735" y="-10" />
        <di:waypoint x="1400" y="-10" />
        <di:waypoint x="1400" y="460" />
        <di:waypoint x="1450" y="460" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1393" y="222" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
