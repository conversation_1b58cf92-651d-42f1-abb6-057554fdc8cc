<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:flowable="http://flowable.org/bpmn" xmlns:activiti="http://activiti.org/bpmn" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" targetNamespace="http://bpmn.io/schema/bpmn">
  <process id="mp_payment" name="物资采购合同付款" isExecutable="true">
    <startEvent id="startEvent_1" name="开始" flowable:formKey="/mach/apvContractPayInfo/auditForm">
      <outgoing>Flow_1lgvhtb</outgoing>
    </startEvent>
    <userTask id="contract_resp" name="合同责任人" flowable:assignee="${respUser}">
      <incoming>Flow_0e7tbsi</incoming>
      <outgoing>Flow_08onq4u</outgoing>
    </userTask>
    <sequenceFlow id="Flow_1lgvhtb" sourceRef="startEvent_1" targetRef="supply_chain_center" />
    <userTask id="supply_chain_center" name="供应链中心经理" flowable:assignee="${supplyUser}">
      <incoming>Flow_08onq4u</incoming>
      <incoming>Flow_0qe05m7</incoming>
      <incoming>Flow_1lgvhtb</incoming>
      <outgoing>Flow_1rplck0</outgoing>
    </userTask>
    <userTask id="senior" name="分管高管" flowable:assignee="${seniorUser}">
      <incoming>Flow_0nntpek</incoming>
      <incoming>Flow_0puilr3</incoming>
      <outgoing>Flow_0rugtbv</outgoing>
    </userTask>
    <sequenceFlow id="Flow_1rplck0" sourceRef="supply_chain_center" targetRef="Gateway_18jbd7l" />
    <userTask id="fpd" name="计财部" flowable:assignee="${fpdUser}">
      <incoming>Flow_13m6nhi</incoming>
      <incoming>Flow_1suy601</incoming>
      <outgoing>Flow_1cf6odo</outgoing>
    </userTask>
    <sequenceFlow id="Flow_0rugtbv" sourceRef="senior" targetRef="Gateway_1k15ind" />
    <userTask id="gm" name="总经理" flowable:assignee="sunchaobo">
      <incoming>Flow_15c3gz2</incoming>
      <outgoing>Flow_14775qg</outgoing>
    </userTask>
    <sequenceFlow id="Flow_1cf6odo" sourceRef="fpd" targetRef="Gateway_1hjjh7x" />
    <endEvent id="Event_0bpy8j1">
      <incoming>Flow_1l5lfok</incoming>
    </endEvent>
    <exclusiveGateway id="Gateway_0n8ds60">
      <incoming>Flow_0k5269v</incoming>
      <incoming>Flow_11iulpd</incoming>
      <incoming>Flow_0zrwpc3</incoming>
      <incoming>Flow_16uic6i</incoming>
      <outgoing>Flow_0e7tbsi</outgoing>
      <outgoing>Flow_0qe05m7</outgoing>
      <outgoing>Flow_0puilr3</outgoing>
      <outgoing>Flow_13m6nhi</outgoing>
    </exclusiveGateway>
    <sequenceFlow id="Flow_08onq4u" sourceRef="contract_resp" targetRef="supply_chain_center" />
    <exclusiveGateway id="Gateway_18jbd7l">
      <incoming>Flow_1rplck0</incoming>
      <outgoing>Flow_0nntpek</outgoing>
      <outgoing>Flow_0k5269v</outgoing>
    </exclusiveGateway>
    <sequenceFlow id="Flow_0nntpek" name="通过" sourceRef="Gateway_18jbd7l" targetRef="senior">
      <conditionExpression xsi:type="tFormalExpression">${pass==1}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="Flow_0k5269v" name="未通过" sourceRef="Gateway_18jbd7l" targetRef="Gateway_0n8ds60">
      <conditionExpression xsi:type="tFormalExpression">${pass==0}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="Flow_0e7tbsi" name="驳回发起人" sourceRef="Gateway_0n8ds60" targetRef="contract_resp">
      <conditionExpression xsi:type="tFormalExpression">${back==1}</conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="Gateway_1k15ind">
      <incoming>Flow_0rugtbv</incoming>
      <outgoing>Flow_11iulpd</outgoing>
      <outgoing>Flow_06fzbtn</outgoing>
      <outgoing>Flow_1suy601</outgoing>
    </exclusiveGateway>
    <sequenceFlow id="Flow_11iulpd" name="未通过" sourceRef="Gateway_1k15ind" targetRef="Gateway_0n8ds60">
      <conditionExpression xsi:type="tFormalExpression">${pass==0}</conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="Gateway_1hjjh7x">
      <incoming>Flow_1cf6odo</incoming>
      <outgoing>Flow_15c3gz2</outgoing>
      <outgoing>Flow_0zrwpc3</outgoing>
    </exclusiveGateway>
    <sequenceFlow id="Flow_15c3gz2" name="复核" sourceRef="Gateway_1hjjh7x" targetRef="gm">
      <conditionExpression xsi:type="tFormalExpression">${pass==1}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="Flow_0zrwpc3" name="未通过" sourceRef="Gateway_1hjjh7x" targetRef="Gateway_0n8ds60">
      <conditionExpression xsi:type="tFormalExpression">${pass==0}</conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="Gateway_1ggrr1z">
      <incoming>Flow_14775qg</incoming>
      <outgoing>Flow_16uic6i</outgoing>
      <outgoing>Flow_1806afr</outgoing>
    </exclusiveGateway>
    <sequenceFlow id="Flow_14775qg" sourceRef="gm" targetRef="Gateway_1ggrr1z" />
    <sequenceFlow id="Flow_16uic6i" name="未通过" sourceRef="Gateway_1ggrr1z" targetRef="Gateway_0n8ds60">
      <conditionExpression xsi:type="tFormalExpression">${pass==0}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="Flow_0qe05m7" name="驳回供应链" sourceRef="Gateway_0n8ds60" targetRef="supply_chain_center">
      <conditionExpression xsi:type="tFormalExpression">${back==2}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="Flow_0puilr3" name="驳回分管" sourceRef="Gateway_0n8ds60" targetRef="senior">
      <conditionExpression xsi:type="tFormalExpression">${back==3}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="Flow_13m6nhi" name="驳回计财" sourceRef="Gateway_0n8ds60" targetRef="fpd">
      <conditionExpression xsi:type="tFormalExpression">${back==4}</conditionExpression>
    </sequenceFlow>
    <userTask id="fpd_pay" name="计财部(付款)" flowable:assignee="${fpdPayUser}">
      <incoming>Flow_06fzbtn</incoming>
      <incoming>Flow_1806afr</incoming>
      <outgoing>Flow_1l5lfok</outgoing>
    </userTask>
    <sequenceFlow id="Flow_06fzbtn" name="小于等于2w" sourceRef="Gateway_1k15ind" targetRef="fpd_pay">
      <conditionExpression xsi:type="tFormalExpression">${pay&lt;2 &amp;&amp; pass ==1}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="Flow_1suy601" name="大于2w" sourceRef="Gateway_1k15ind" targetRef="fpd">
      <conditionExpression xsi:type="tFormalExpression">${pay&gt;2 &amp;&amp; pass ==1}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="Flow_1l5lfok" sourceRef="fpd_pay" targetRef="Event_0bpy8j1" />
    <sequenceFlow id="Flow_1806afr" name="通过" sourceRef="Gateway_1ggrr1z" targetRef="fpd_pay">
      <conditionExpression xsi:type="tFormalExpression">${pass=1}</conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_flow">
    <bpmndi:BPMNPlane id="BPMNPlane_flow" bpmnElement="mp_payment">
      <bpmndi:BPMNShape id="BPMNShape_startEvent_1" bpmnElement="startEvent_1">
        <dc:Bounds x="222" y="200" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="229" y="243" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0m9ro12_di" bpmnElement="contract_resp">
        <dc:Bounds x="340" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1qdrax2_di" bpmnElement="supply_chain_center">
        <dc:Bounds x="500" y="178" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_199ohlx_di" bpmnElement="senior">
        <dc:Bounds x="750" y="178" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0amhrfy_di" bpmnElement="fpd">
        <dc:Bounds x="1140" y="178" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0lror3k_di" bpmnElement="gm">
        <dc:Bounds x="1450" y="178" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0bpy8j1_di" bpmnElement="Event_0bpy8j1">
        <dc:Bounds x="1772" y="372" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0n8ds60_di" bpmnElement="Gateway_0n8ds60" isMarkerVisible="true">
        <dc:Bounds x="645" y="-35" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_18jbd7l_di" bpmnElement="Gateway_18jbd7l" isMarkerVisible="true">
        <dc:Bounds x="645" y="193" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1k15ind_di" bpmnElement="Gateway_1k15ind" isMarkerVisible="true">
        <dc:Bounds x="905" y="193" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1hjjh7x_di" bpmnElement="Gateway_1hjjh7x" isMarkerVisible="true">
        <dc:Bounds x="1305" y="193" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1ggrr1z_di" bpmnElement="Gateway_1ggrr1z" isMarkerVisible="true">
        <dc:Bounds x="1625" y="193" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_05xdw9t_di" bpmnElement="fpd_pay">
        <dc:Bounds x="1140" y="350" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1lgvhtb_di" bpmnElement="Flow_1lgvhtb">
        <di:waypoint x="258" y="218" />
        <di:waypoint x="500" y="218" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rplck0_di" bpmnElement="Flow_1rplck0">
        <di:waypoint x="600" y="218" />
        <di:waypoint x="645" y="218" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rugtbv_di" bpmnElement="Flow_0rugtbv">
        <di:waypoint x="850" y="218" />
        <di:waypoint x="905" y="218" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1cf6odo_di" bpmnElement="Flow_1cf6odo">
        <di:waypoint x="1240" y="218" />
        <di:waypoint x="1305" y="218" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1046" y="291" width="60" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08onq4u_di" bpmnElement="Flow_08onq4u">
        <di:waypoint x="390" y="160" />
        <di:waypoint x="390" y="218" />
        <di:waypoint x="500" y="218" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0nntpek_di" bpmnElement="Flow_0nntpek">
        <di:waypoint x="695" y="218" />
        <di:waypoint x="750" y="218" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="712" y="200" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0k5269v_di" bpmnElement="Flow_0k5269v">
        <di:waypoint x="670" y="193" />
        <di:waypoint x="670" y="15" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="669" y="99" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0e7tbsi_di" bpmnElement="Flow_0e7tbsi">
        <di:waypoint x="645" y="-10" />
        <di:waypoint x="390" y="-10" />
        <di:waypoint x="390" y="80" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="458" y="-28" width="55" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11iulpd_di" bpmnElement="Flow_11iulpd">
        <di:waypoint x="930" y="193" />
        <di:waypoint x="930" y="-10" />
        <di:waypoint x="695" y="-10" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="929" y="87" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15c3gz2_di" bpmnElement="Flow_15c3gz2">
        <di:waypoint x="1355" y="218" />
        <di:waypoint x="1450" y="218" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1393" y="200" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zrwpc3_di" bpmnElement="Flow_0zrwpc3">
        <di:waypoint x="1330" y="193" />
        <di:waypoint x="1330" y="-10" />
        <di:waypoint x="695" y="-10" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1329" y="87" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14775qg_di" bpmnElement="Flow_14775qg">
        <di:waypoint x="1550" y="218" />
        <di:waypoint x="1625" y="218" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16uic6i_di" bpmnElement="Flow_16uic6i">
        <di:waypoint x="1650" y="193" />
        <di:waypoint x="1650" y="-10" />
        <di:waypoint x="695" y="-10" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1649" y="82" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qe05m7_di" bpmnElement="Flow_0qe05m7">
        <di:waypoint x="645" y="-10" />
        <di:waypoint x="550" y="-10" />
        <di:waypoint x="550" y="178" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="571" y="-28" width="55" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0puilr3_di" bpmnElement="Flow_0puilr3">
        <di:waypoint x="695" y="-10" />
        <di:waypoint x="800" y="-10" />
        <di:waypoint x="800" y="178" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="726" y="-28" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13m6nhi_di" bpmnElement="Flow_13m6nhi">
        <di:waypoint x="695" y="-10" />
        <di:waypoint x="1190" y="-10" />
        <di:waypoint x="1190" y="178" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="922" y="-28" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_06fzbtn_di" bpmnElement="Flow_06fzbtn">
        <di:waypoint x="930" y="243" />
        <di:waypoint x="930" y="390" />
        <di:waypoint x="1140" y="390" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="916" y="314" width="60" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1suy601_di" bpmnElement="Flow_1suy601">
        <di:waypoint x="955" y="218" />
        <di:waypoint x="1140" y="218" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1030" y="200" width="38" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1l5lfok_di" bpmnElement="Flow_1l5lfok">
        <di:waypoint x="1240" y="390" />
        <di:waypoint x="1772" y="390" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1806afr_di" bpmnElement="Flow_1806afr">
        <di:waypoint x="1650" y="243" />
        <di:waypoint x="1650" y="310" />
        <di:waypoint x="1190" y="310" />
        <di:waypoint x="1190" y="350" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1654" y="272" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
